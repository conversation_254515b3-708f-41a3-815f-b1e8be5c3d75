"""
推理模块
"""
import torch
import torch.nn.functional as F
from transformers import AutoTokenizer
from typing import List, Dict, Optional, Union
import json
import asyncio

from .config import config
from .model import DocumentLearnerModel
from .data_processor import DocumentProcessor


class DocumentLearnerInference:
    """文档学习模型推理器"""
    
    def __init__(
        self, 
        model_path: str,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        self.device = device
        
        # 加载模型和tokenizer
        self.model = DocumentLearnerModel.from_pretrained(model_path)
        self.model.to(device)
        self.model.eval()
        
        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 文档处理器
        self.doc_processor = DocumentProcessor()
        
        print(f"模型已加载到 {device}")
    
    def generate_text(
        self, 
        prompt: str,
        max_new_tokens: int = None,
        temperature: float = None,
        top_p: float = None,
        do_sample: bool = None
    ) -> str:
        """生成文本"""
        # 使用配置中的默认值
        max_new_tokens = max_new_tokens or config.inference.max_new_tokens
        temperature = temperature or config.inference.temperature
        top_p = top_p or config.inference.top_p
        do_sample = do_sample if do_sample is not None else config.inference.do_sample
        
        # 编码输入
        inputs = self.tokenizer(
            prompt,
            return_tensors='pt',
            max_length=config.data.max_seq_length,
            truncation=True
        ).to(self.device)
        
        with torch.no_grad():
            # 生成
            if do_sample:
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    do_sample=True,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            else:
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_new_tokens,
                    do_sample=False,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
        
        # 解码输出
        generated_text = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:], 
            skip_special_tokens=True
        )
        
        return generated_text.strip()
    
    async def learn_from_document(self, document_source: str) -> Dict[str, any]:
        """从文档学习"""
        print(f"正在学习文档: {document_source}")
        
        # 处理文档
        if document_source.startswith('http'):
            chunks = await self.doc_processor.fetch_web_document(document_source)
        elif document_source.endswith('.md'):
            chunks = await self.doc_processor.process_markdown_file(document_source)
        else:
            chunks = await self.doc_processor.process_text_file(document_source)
        
        if not chunks:
            return {"error": "无法处理文档"}
        
        # 分析文档内容
        document_summary = self._analyze_document(chunks)
        
        return {
            "document_source": document_source,
            "chunks_count": len(chunks),
            "summary": document_summary,
            "learned_concepts": self._extract_concepts(chunks)
        }
    
    def answer_question(self, question: str, context: str = "") -> str:
        """回答问题"""
        if context:
            prompt = f"文档内容：{context}\n\n问题：{question}\n\n答案："
        else:
            prompt = f"问题：{question}\n\n答案："
        
        answer = self.generate_text(prompt)
        return answer
    
    def generate_code(self, description: str, language: str = "javascript") -> str:
        """生成代码"""
        prompt = f"请用{language}编写代码来实现：{description}\n\n代码：\n```{language}\n"
        
        code = self.generate_text(prompt, max_new_tokens=512)
        
        # 清理代码输出
        if "```" in code:
            code = code.split("```")[0]
        
        return code.strip()
    
    def explain_concept(self, concept: str, context: str = "") -> str:
        """解释概念"""
        if context:
            prompt = f"基于以下文档内容：{context}\n\n请解释什么是{concept}："
        else:
            prompt = f"请解释什么是{concept}："
        
        explanation = self.generate_text(prompt)
        return explanation
    
    def _analyze_document(self, chunks: List) -> str:
        """分析文档内容"""
        # 合并所有块的内容
        full_content = "\n\n".join([chunk.content for chunk in chunks[:3]])  # 只取前3个块避免太长
        
        prompt = f"请总结以下文档的主要内容：\n\n{full_content}\n\n总结："
        
        summary = self.generate_text(prompt, max_new_tokens=200)
        return summary
    
    def _extract_concepts(self, chunks: List) -> List[str]:
        """提取关键概念"""
        concepts = []
        
        for chunk in chunks[:5]:  # 只处理前5个块
            prompt = f"从以下文本中提取3个最重要的技术概念或关键词：\n\n{chunk.content}\n\n关键概念："
            
            extracted = self.generate_text(prompt, max_new_tokens=100)
            
            # 简单解析提取的概念
            concept_lines = extracted.split('\n')
            for line in concept_lines:
                line = line.strip()
                if line and not line.startswith('关键概念'):
                    # 移除编号和特殊字符
                    concept = line.replace('1.', '').replace('2.', '').replace('3.', '').replace('-', '').strip()
                    if concept and len(concept) > 2:
                        concepts.append(concept)
        
        # 去重并返回
        return list(set(concepts))[:10]  # 最多返回10个概念
    
    def interactive_chat(self):
        """交互式聊天"""
        print("文档学习助手已启动！输入 'quit' 退出，输入 'learn <url/path>' 学习新文档")
        
        learned_documents = []
        
        while True:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() == 'quit':
                print("再见！")
                break
            
            if user_input.startswith('learn '):
                doc_path = user_input[6:].strip()
                try:
                    result = asyncio.run(self.learn_from_document(doc_path))
                    if "error" in result:
                        print(f"助手: {result['error']}")
                    else:
                        learned_documents.append(result)
                        print(f"助手: 已学习文档 '{doc_path}'")
                        print(f"文档摘要: {result['summary']}")
                        print(f"学到的概念: {', '.join(result['learned_concepts'][:5])}")
                except Exception as e:
                    print(f"助手: 学习文档时出错: {e}")
                continue
            
            # 构建上下文
            context = ""
            if learned_documents:
                recent_doc = learned_documents[-1]  # 使用最近学习的文档
                context = f"基于已学习的文档 '{recent_doc['document_source']}'"
            
            # 生成回答
            try:
                response = self.answer_question(user_input, context)
                print(f"助手: {response}")
            except Exception as e:
                print(f"助手: 生成回答时出错: {e}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python inference.py <model_path>")
        return
    
    model_path = sys.argv[1]
    
    try:
        # 创建推理器
        inferencer = DocumentLearnerInference(model_path)
        
        # 启动交互式聊天
        inferencer.interactive_chat()
        
    except Exception as e:
        print(f"启动推理器时出错: {e}")


if __name__ == "__main__":
    main()
