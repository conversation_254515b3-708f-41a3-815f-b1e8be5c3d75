"""
数据处理模块
"""
import os
import re
import json
import asyncio
import aiofiles
from typing import List, Dict, Tuple, Optional, AsyncGenerator
from dataclasses import dataclass
import requests
from bs4 import BeautifulSoup
import markdown
from transformers import AutoTokenizer
from .config import config


@dataclass
class DocumentChunk:
    """文档块"""
    content: str
    source: str
    chunk_id: int
    metadata: Dict


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, tokenizer_name: str = "microsoft/DialoGPT-medium"):
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    async def process_markdown_file(self, file_path: str) -> List[DocumentChunk]:
        """处理Markdown文件"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        # 转换markdown到HTML再到纯文本
        html = markdown.markdown(content)
        soup = BeautifulSoup(html, 'html.parser')
        text = soup.get_text()
        
        return self._chunk_text(text, os.path.basename(file_path))
    
    async def process_text_file(self, file_path: str) -> List[DocumentChunk]:
        """处理纯文本文件"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        return self._chunk_text(content, os.path.basename(file_path))
    
    async def fetch_web_document(self, url: str) -> List[DocumentChunk]:
        """获取网页文档"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式标签
            for script in soup(["script", "style"]):
                script.decompose()
            
            text = soup.get_text()
            return self._chunk_text(text, url)
        
        except Exception as e:
            print(f"获取网页失败 {url}: {e}")
            return []
    
    def _chunk_text(self, text: str, source: str) -> List[DocumentChunk]:
        """将文本分块"""
        # 清理文本
        text = self._clean_text(text)
        
        # 按段落分割
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        chunks = []
        current_chunk = ""
        chunk_id = 0
        
        for paragraph in paragraphs:
            # 检查添加这个段落是否会超过最大长度
            test_chunk = current_chunk + "\n\n" + paragraph if current_chunk else paragraph
            
            if len(self.tokenizer.encode(test_chunk)) <= config.data.doc_chunk_size:
                current_chunk = test_chunk
            else:
                # 保存当前块
                if current_chunk:
                    chunks.append(DocumentChunk(
                        content=current_chunk,
                        source=source,
                        chunk_id=chunk_id,
                        metadata={"length": len(current_chunk)}
                    ))
                    chunk_id += 1
                
                # 开始新块
                current_chunk = paragraph
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(DocumentChunk(
                content=current_chunk,
                source=source,
                chunk_id=chunk_id,
                metadata={"length": len(current_chunk)}
            ))
        
        return chunks
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符但保留基本标点
        text = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)\[\]\{\}\"\'\/\\]', '', text)
        
        # 规范化换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()


class DatasetBuilder:
    """数据集构建器"""
    
    def __init__(self, processor: DocumentProcessor):
        self.processor = processor
    
    async def build_training_dataset(self, document_paths: List[str]) -> List[Dict]:
        """构建训练数据集"""
        all_chunks = []
        
        for path in document_paths:
            if path.startswith('http'):
                chunks = await self.processor.fetch_web_document(path)
            elif path.endswith('.md'):
                chunks = await self.processor.process_markdown_file(path)
            else:
                chunks = await self.processor.process_text_file(path)
            
            all_chunks.extend(chunks)
        
        # 生成训练样本
        training_samples = []
        
        for chunk in all_chunks:
            # 生成掩码语言建模样本
            mlm_samples = self._generate_mlm_samples(chunk)
            training_samples.extend(mlm_samples)
            
            # 生成问答样本
            qa_samples = self._generate_qa_samples(chunk)
            training_samples.extend(qa_samples)
        
        return training_samples
    
    def _generate_mlm_samples(self, chunk: DocumentChunk) -> List[Dict]:
        """生成掩码语言建模样本"""
        samples = []
        sentences = chunk.content.split('.')
        
        for sentence in sentences:
            if len(sentence.strip()) < 10:  # 跳过太短的句子
                continue
            
            words = sentence.strip().split()
            if len(words) < 3:
                continue
            
            # 随机掩码15%的词
            masked_sentence = self._mask_tokens(words)
            
            samples.append({
                'input_text': masked_sentence,
                'target_text': sentence.strip(),
                'task_type': 'mlm',
                'source': chunk.source
            })
        
        return samples
    
    def _generate_qa_samples(self, chunk: DocumentChunk) -> List[Dict]:
        """生成问答样本"""
        samples = []
        
        # 简单的问答生成策略
        sentences = [s.strip() for s in chunk.content.split('.') if s.strip()]
        
        for i, sentence in enumerate(sentences):
            if len(sentence) < 20:
                continue
            
            # 生成"这是什么"类型的问题
            question = f"根据文档，{sentence[:20]}...是什么意思？"
            
            # 使用当前句子和上下文作为答案
            context_start = max(0, i-1)
            context_end = min(len(sentences), i+2)
            answer = '. '.join(sentences[context_start:context_end])
            
            samples.append({
                'input_text': f"文档内容：{chunk.content}\n\n问题：{question}",
                'target_text': answer,
                'task_type': 'qa',
                'source': chunk.source
            })
        
        return samples[:3]  # 限制每个块的问答样本数量
    
    def _mask_tokens(self, words: List[str]) -> str:
        """掩码词汇"""
        import random
        
        masked_words = []
        for word in words:
            if random.random() < 0.15:  # 15%的概率掩码
                masked_words.append('[MASK]')
            else:
                masked_words.append(word)
        
        return ' '.join(masked_words)


async def main():
    """测试数据处理"""
    processor = DocumentProcessor()
    builder = DatasetBuilder(processor)
    
    # 测试处理JavaScript文档
    js_docs = [
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction"
    ]
    
    samples = await builder.build_training_dataset(js_docs)
    
    print(f"生成了 {len(samples)} 个训练样本")
    for i, sample in enumerate(samples[:3]):
        print(f"\n样本 {i+1}:")
        print(f"任务类型: {sample['task_type']}")
        print(f"输入: {sample['input_text'][:100]}...")
        print(f"目标: {sample['target_text'][:100]}...")


if __name__ == "__main__":
    asyncio.run(main())
