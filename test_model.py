#!/usr/bin/env python3
"""
模型测试脚本
"""
import sys
import os
import asyncio
import argparse

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.inference import DocumentLearnerInference


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="测试文档学习模型")
    
    parser.add_argument(
        "model_path",
        type=str,
        help="训练好的模型路径"
    )
    
    parser.add_argument(
        "--test_document",
        type=str,
        help="测试文档路径或URL"
    )
    
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="启动交互式模式"
    )
    
    return parser.parse_args()


async def test_basic_functionality(inferencer: DocumentLearnerInference):
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    # 测试文本生成
    print("\n1. 测试文本生成:")
    prompt = "JavaScript是一种"
    response = inferencer.generate_text(prompt, max_new_tokens=50)
    print(f"输入: {prompt}")
    print(f"输出: {response}")
    
    # 测试问答
    print("\n2. 测试问答:")
    question = "什么是变量？"
    answer = inferencer.answer_question(question)
    print(f"问题: {question}")
    print(f"答案: {answer}")
    
    # 测试代码生成
    print("\n3. 测试代码生成:")
    description = "创建一个函数来计算两个数的和"
    code = inferencer.generate_code(description, "javascript")
    print(f"描述: {description}")
    print(f"生成的代码:\n{code}")
    
    # 测试概念解释
    print("\n4. 测试概念解释:")
    concept = "函数"
    explanation = inferencer.explain_concept(concept)
    print(f"概念: {concept}")
    print(f"解释: {explanation}")


async def test_document_learning(inferencer: DocumentLearnerInference, document_path: str):
    """测试文档学习功能"""
    print(f"\n📚 测试文档学习功能: {document_path}")
    
    try:
        # 学习文档
        result = await inferencer.learn_from_document(document_path)
        
        if "error" in result:
            print(f"❌ 学习文档失败: {result['error']}")
            return
        
        print(f"✅ 成功学习文档")
        print(f"文档块数量: {result['chunks_count']}")
        print(f"文档摘要: {result['summary']}")
        print(f"学到的概念: {', '.join(result['learned_concepts'][:5])}")
        
        # 基于学习的文档回答问题
        print("\n📝 基于学习的文档回答问题:")
        questions = [
            "这个文档主要讲什么？",
            "有哪些重要的概念？",
            "能给我一个例子吗？"
        ]
        
        for question in questions:
            answer = inferencer.answer_question(question, result['summary'])
            print(f"Q: {question}")
            print(f"A: {answer}\n")
    
    except Exception as e:
        print(f"❌ 测试文档学习时出错: {e}")


def run_performance_test(inferencer: DocumentLearnerInference):
    """运行性能测试"""
    print("\n⚡ 性能测试...")
    
    import time
    
    # 测试生成速度
    prompts = [
        "JavaScript中的变量",
        "如何创建函数",
        "什么是循环",
        "数组的使用方法",
        "对象的属性"
    ]
    
    total_time = 0
    total_tokens = 0
    
    for prompt in prompts:
        start_time = time.time()
        response = inferencer.generate_text(prompt, max_new_tokens=30)
        end_time = time.time()
        
        generation_time = end_time - start_time
        token_count = len(response.split())
        
        total_time += generation_time
        total_tokens += token_count
        
        print(f"提示: {prompt}")
        print(f"生成时间: {generation_time:.2f}s, 词数: {token_count}")
    
    avg_time = total_time / len(prompts)
    tokens_per_second = total_tokens / total_time
    
    print(f"\n📊 性能统计:")
    print(f"平均生成时间: {avg_time:.2f}s")
    print(f"生成速度: {tokens_per_second:.1f} tokens/s")


async def main():
    """主函数"""
    args = parse_args()
    
    print("🤖 文档学习模型测试")
    print("=" * 50)
    
    # 检查模型路径
    if not os.path.exists(args.model_path):
        print(f"❌ 模型路径不存在: {args.model_path}")
        return
    
    try:
        # 加载模型
        print(f"📦 加载模型: {args.model_path}")
        inferencer = DocumentLearnerInference(args.model_path)
        print("✅ 模型加载成功")
        
        if args.interactive:
            # 交互式模式
            inferencer.interactive_chat()
        else:
            # 自动测试模式
            await test_basic_functionality(inferencer)
            
            if args.test_document:
                await test_document_learning(inferencer, args.test_document)
            
            run_performance_test(inferencer)
            
            print("\n✅ 所有测试完成！")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
