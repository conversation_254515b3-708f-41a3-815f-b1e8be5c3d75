# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt

# 训练输出
outputs/
models/
logs/
cache/
checkpoints/
wandb/

# 数据文件
data/
*.csv
*.json
*.txt
*.md
!README.md
!requirements.txt

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# 系统文件
.DS_Store
Thumbs.db

# 备份文件
*.backup
*.bak
*.tar.gz
*.zip

# 临时文件
tmp/
temp/
*.tmp

# 配置文件 (如果包含敏感信息)
config_local.yaml
secrets.yaml
