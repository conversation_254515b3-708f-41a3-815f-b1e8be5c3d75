#!/usr/bin/env python3
"""
安装和设置脚本
"""
import os
import sys
import subprocess
import platform


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ 检测到GPU: {gpu_name} (共{gpu_count}个)")
            return True
        else:
            print("⚠ 未检测到GPU，将使用CPU训练")
            return False
    except ImportError:
        print("⚠ PyTorch未安装，无法检测GPU")
        return False


def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def create_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = [
        "data",
        "outputs", 
        "models",
        "logs",
        "cache"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  ✓ {directory}/")
    
    print("✅ 目录创建完成")


def setup_wandb():
    """设置Wandb"""
    print("\n🔧 Wandb设置 (可选)")
    print("Wandb用于训练监控和可视化")
    
    choice = input("是否要设置Wandb? (y/n): ").strip().lower()
    
    if choice == 'y':
        try:
            import wandb
            print("请访问 https://wandb.ai 注册账号并获取API密钥")
            api_key = input("请输入Wandb API密钥 (留空跳过): ").strip()
            
            if api_key:
                # 设置API密钥
                os.environ['WANDB_API_KEY'] = api_key
                
                # 测试登录
                wandb.login(key=api_key)
                print("✅ Wandb设置成功")
            else:
                print("⚠ 跳过Wandb设置")
        except ImportError:
            print("❌ Wandb未安装")
        except Exception as e:
            print(f"❌ Wandb设置失败: {e}")
    else:
        print("⚠ 跳过Wandb设置")


def run_quick_test():
    """运行快速测试"""
    print("\n🧪 运行快速测试...")
    
    try:
        # 测试导入
        sys.path.append('src')
        
        from src.config import config
        print("✓ 配置模块导入成功")
        
        from src.model import create_model
        print("✓ 模型模块导入成功")
        
        from src.data_processor import DocumentProcessor
        print("✓ 数据处理模块导入成功")
        
        # 测试模型创建
        model = create_model()
        print("✓ 模型创建成功")
        
        print("✅ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 安装完成！")
    print("\n📋 后续步骤:")
    print("1. 训练模型:")
    print("   python train.py --documents <文档路径或URL>")
    print("\n2. 运行示例:")
    print("   python example.py")
    print("\n3. 测试模型:")
    print("   python test_model.py <模型路径>")
    print("\n4. 交互式使用:")
    print("   python test_model.py <模型路径> --interactive")
    print("\n📖 更多信息请查看 README.md")


def main():
    """主安装流程"""
    print("🚀 文档学习模型安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查操作系统
    os_name = platform.system()
    print(f"✅ 操作系统: {os_name}")
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 安装失败")
        return
    
    # 检查GPU
    check_gpu()
    
    # 设置Wandb
    setup_wandb()
    
    # 运行测试
    if not run_quick_test():
        print("⚠ 测试失败，但安装可能仍然成功")
    
    # 显示后续步骤
    show_next_steps()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出错: {e}")
        import traceback
        traceback.print_exc()
