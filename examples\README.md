# 多语言学习示例

这个目录包含了多语言文档学习的示例和工具。

## 📁 文件说明

- `multi_language_training.py` - 多语言模型训练脚本
- `test_multilang.py` - 多语言能力测试脚本
- `docs/` - 示例文档目录

## 🚀 快速开始

### 1. 训练多语言模型

```bash
# 训练Python专用模型
python examples/multi_language_training.py
# 选择选项1，然后输入 python

# 训练多语言通用模型
python examples/multi_language_training.py
# 选择选项2

# 使用本地文档训练
python examples/multi_language_training.py
# 选择选项3
```

### 2. 测试多语言能力

```bash
# 全面测试
python examples/test_multilang.py
# 选择模型，然后选择测试模式

# 交互式测试
python examples/test_multilang.py
# 选择模型，然后选择选项5
```

## 🌍 支持的语言

### 在线文档训练
- **Python**: Python官方教程
- **Java**: Oracle Java教程
- **C++**: CPP参考文档
- **Go**: Go官方教程
- **Rust**: Rust官方书籍
- **React**: React官方文档
- **Django**: Django官方文档
- **TensorFlow**: TensorFlow指南

### 本地文档示例
- `docs/python_tutorial.md` - Python基础教程
- `docs/go_tutorial.md` - Go语言基础
- `docs/react_tutorial.md` - React组件教程

## 💡 使用技巧

### 1. 语言特定训练
如果你主要使用某种语言，建议训练该语言的专用模型：

```bash
python examples/multi_language_training.py
# 选择选项1，输入目标语言
```

### 2. 多语言通用训练
如果你需要学习多种语言，建议训练多语言通用模型：

```bash
python examples/multi_language_training.py
# 选择选项2
```

### 3. 自定义文档训练
你可以使用自己的文档进行训练：

```python
from src.trainer import main_training_pipeline

# 使用自定义文档
custom_docs = [
    "path/to/your/doc1.md",
    "https://your-favorite-tutorial.com",
    "path/to/your/doc2.txt"
]

trainer = main_training_pipeline(custom_docs)
```

## 🧪 测试功能

### 基本能力测试
- 问答能力
- 代码生成
- 概念解释

### 跨语言能力测试
- 语言比较
- 语法差异解释
- 最佳实践推荐

### 学习新语言测试
- 动态文档学习
- 概念提取
- 知识应用

## 📊 性能对比

不同训练策略的特点：

| 策略 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 单语言专用 | 该语言理解深入 | 不支持其他语言 | 专精某种语言 |
| 多语言通用 | 支持多种语言 | 单语言深度有限 | 学习多种语言 |
| 本地文档 | 训练速度快 | 知识范围有限 | 快速验证 |

## 🔧 自定义配置

你可以通过修改配置来优化训练：

```python
# 修改模型大小
config.model.hidden_size = 256  # 更小的模型
config.model.num_hidden_layers = 6

# 修改训练参数
config.training.batch_size = 8  # 适应GPU内存
config.training.learning_rate = 1e-4  # 更保守的学习率
```

## 📝 添加新语言支持

要添加新语言支持，编辑 `multi_language_training.py`：

```python
LANGUAGE_DOCS['新语言'] = [
    "文档URL1",
    "文档URL2",
    # ...
]
```

然后在 `test_multilang.py` 中添加测试用例：

```python
TEST_CASES['新语言'] = {
    'questions': ["问题1", "问题2"],
    'code_tasks': ["任务1", "任务2"],
    'concepts': ["概念1", "概念2"]
}
```

## 🎯 最佳实践

1. **渐进式学习**: 先学一种语言，再扩展到多语言
2. **文档质量**: 选择官方或权威的文档进行训练
3. **平衡训练**: 多语言训练时保持各语言文档数量平衡
4. **定期评估**: 使用测试脚本定期评估模型能力
5. **增量学习**: 可以在已训练模型基础上学习新语言

## 🔍 故障排除

### 常见问题

1. **内存不足**
   - 减小batch_size
   - 使用梯度累积
   - 减小模型大小

2. **训练速度慢**
   - 使用GPU训练
   - 减少文档数量
   - 使用本地文档

3. **模型效果差**
   - 增加训练轮数
   - 使用更多高质量文档
   - 调整学习率

### 调试技巧

```bash
# 查看训练日志
tail -f logs/training.log

# 监控GPU使用
nvidia-smi -l 1

# 测试单个文档处理
python -c "
from src.data_processor import DocumentProcessor
import asyncio
processor = DocumentProcessor()
chunks = asyncio.run(processor.process_text_file('your_doc.md'))
print(f'处理了 {len(chunks)} 个文档块')
"
```

## 📚 学习资源

- [Transformer模型原理](https://jalammar.github.io/illustrated-transformer/)
- [多语言NLP最佳实践](https://huggingface.co/docs/transformers/multilingual)
- [小模型训练技巧](https://github.com/rasbt/LLMs-from-scratch)

## 🤝 贡献指南

欢迎贡献新的语言支持和改进：

1. Fork项目
2. 添加新语言的文档链接
3. 创建对应的测试用例
4. 提交Pull Request

## 📄 许可证

与主项目相同的MIT许可证。
