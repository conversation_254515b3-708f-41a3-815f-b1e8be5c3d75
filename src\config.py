"""
配置管理模块
"""
import yaml
import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class ModelConfig:
    """模型配置"""
    name: str = "DocumentLearner"
    vocab_size: int = 32000
    hidden_size: int = 512
    num_hidden_layers: int = 8
    num_attention_heads: int = 8
    intermediate_size: int = 2048
    max_position_embeddings: int = 2048
    dropout: float = 0.1
    layer_norm_eps: float = 1e-12


@dataclass
class TrainingConfig:
    """训练配置"""
    batch_size: int = 16
    learning_rate: float = 5e-4
    num_epochs: int = 10
    warmup_steps: int = 1000
    weight_decay: float = 0.01
    gradient_accumulation_steps: int = 4
    max_grad_norm: float = 1.0
    save_steps: int = 500
    eval_steps: int = 500
    logging_steps: int = 100


@dataclass
class DataConfig:
    """数据配置"""
    max_seq_length: int = 1024
    doc_chunk_size: int = 512
    overlap_size: int = 64
    train_split: float = 0.8
    val_split: float = 0.1
    test_split: float = 0.1


@dataclass
class PathConfig:
    """路径配置"""
    data_dir: str = "./data"
    output_dir: str = "./outputs"
    model_dir: str = "./models"
    logs_dir: str = "./logs"
    cache_dir: str = "./cache"


@dataclass
class InferenceConfig:
    """推理配置"""
    max_new_tokens: int = 256
    temperature: float = 0.7
    top_p: float = 0.9
    do_sample: bool = True


class Config:
    """主配置类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
        else:
            config_dict = {}
        
        # 初始化各个配置模块
        self.model = ModelConfig(**config_dict.get('model', {}))
        self.training = TrainingConfig(**config_dict.get('training', {}))
        self.data = DataConfig(**config_dict.get('data', {}))
        self.paths = PathConfig(**config_dict.get('paths', {}))
        self.inference = InferenceConfig(**config_dict.get('inference', {}))
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        dirs = [
            self.paths.data_dir,
            self.paths.output_dir,
            self.paths.model_dir,
            self.paths.logs_dir,
            self.paths.cache_dir
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
    
    def save_config(self, path: str = None):
        """保存配置到文件"""
        if path is None:
            path = self.config_path
        
        config_dict = {
            'model': self.model.__dict__,
            'training': self.training.__dict__,
            'data': self.data.__dict__,
            'paths': self.paths.__dict__,
            'inference': self.inference.__dict__
        }
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)


# 全局配置实例
config = Config()
