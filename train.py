#!/usr/bin/env python3
"""
主训练脚本
"""
import argparse
import json
import os
import sys
from typing import List

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.trainer import main_training_pipeline
from src.config import config


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="训练文档学习模型")
    
    parser.add_argument(
        "--documents",
        type=str,
        nargs="+",
        required=True,
        help="要学习的文档路径或URL列表"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--output_dir",
        type=str,
        help="输出目录"
    )
    
    parser.add_argument(
        "--batch_size",
        type=int,
        help="批次大小"
    )
    
    parser.add_argument(
        "--learning_rate",
        type=float,
        help="学习率"
    )
    
    parser.add_argument(
        "--num_epochs",
        type=int,
        help="训练轮数"
    )
    
    parser.add_argument(
        "--resume_from",
        type=str,
        help="从检查点恢复训练"
    )
    
    parser.add_argument(
        "--wandb_project",
        type=str,
        default="document-learner",
        help="Wandb项目名称"
    )
    
    return parser.parse_args()


def update_config_from_args(args):
    """根据命令行参数更新配置"""
    if args.output_dir:
        config.paths.output_dir = args.output_dir
    
    if args.batch_size:
        config.training.batch_size = args.batch_size
    
    if args.learning_rate:
        config.training.learning_rate = args.learning_rate
    
    if args.num_epochs:
        config.training.num_epochs = args.num_epochs


def validate_documents(documents: List[str]) -> List[str]:
    """验证文档路径"""
    valid_documents = []
    
    for doc in documents:
        if doc.startswith('http'):
            # 网络文档，假设有效
            valid_documents.append(doc)
            print(f"✓ 网络文档: {doc}")
        elif os.path.exists(doc):
            # 本地文件
            valid_documents.append(doc)
            print(f"✓ 本地文档: {doc}")
        else:
            print(f"✗ 文档不存在: {doc}")
    
    return valid_documents


def main():
    """主函数"""
    args = parse_args()
    
    print("=" * 60)
    print("文档学习模型训练")
    print("=" * 60)
    
    # 加载配置
    if os.path.exists(args.config):
        config.load_config()
        print(f"✓ 已加载配置文件: {args.config}")
    else:
        print(f"⚠ 配置文件不存在，使用默认配置: {args.config}")
    
    # 更新配置
    update_config_from_args(args)
    
    # 验证文档
    print("\n验证文档...")
    valid_documents = validate_documents(args.documents)
    
    if not valid_documents:
        print("❌ 没有有效的文档，退出训练")
        return
    
    print(f"✓ 找到 {len(valid_documents)} 个有效文档")
    
    # 显示训练配置
    print("\n训练配置:")
    print(f"  批次大小: {config.training.batch_size}")
    print(f"  学习率: {config.training.learning_rate}")
    print(f"  训练轮数: {config.training.num_epochs}")
    print(f"  输出目录: {config.paths.output_dir}")
    print(f"  模型目录: {config.paths.model_dir}")
    
    # 创建输出目录
    os.makedirs(config.paths.output_dir, exist_ok=True)
    os.makedirs(config.paths.model_dir, exist_ok=True)
    
    # 保存配置
    config_save_path = os.path.join(config.paths.output_dir, "training_config.yaml")
    config.save_config(config_save_path)
    print(f"✓ 训练配置已保存到: {config_save_path}")
    
    # 保存文档列表
    docs_save_path = os.path.join(config.paths.output_dir, "training_documents.json")
    with open(docs_save_path, 'w', encoding='utf-8') as f:
        json.dump(valid_documents, f, indent=2, ensure_ascii=False)
    print(f"✓ 文档列表已保存到: {docs_save_path}")
    
    print("\n开始训练...")
    print("=" * 60)
    
    try:
        # 开始训练
        trainer = main_training_pipeline(valid_documents)
        
        print("\n" + "=" * 60)
        print("✅ 训练完成！")
        print(f"最佳模型保存在: {os.path.join(config.paths.model_dir, 'best_model')}")
        print(f"训练日志保存在: {config.paths.output_dir}")
        
        # 显示如何使用模型
        print("\n如何使用训练好的模型:")
        print(f"python -m src.inference {os.path.join(config.paths.model_dir, 'best_model')}")
        
    except KeyboardInterrupt:
        print("\n⚠ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
