"""
训练模块
"""
import os
import json
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from torch.optim import AdamW
from torch.optim.lr_scheduler import LinearLR
from transformers import AutoTokenizer, get_linear_schedule_with_warmup
from typing import List, Dict, Optional, Tuple
import wandb
from tqdm import tqdm
import numpy as np
from sklearn.metrics import accuracy_score
import asyncio

from .config import config
from .model import create_model, DocumentLearnerModel
from .data_processor import DocumentProcessor, DatasetBuilder


class DocumentDataset(Dataset):
    """文档数据集"""

    def __init__(self, samples: List[Dict], tokenizer: AutoTokenizer, max_length: int = 1024):
        self.samples = samples
        self.tokenizer = tokenizer
        self.max_length = max_length

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.samples[idx]

        # 编码输入和目标
        input_encoding = self.tokenizer(
            sample['input_text'],
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )

        target_encoding = self.tokenizer(
            sample['target_text'],
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )

        return {
            'input_ids': input_encoding['input_ids'].squeeze(),
            'attention_mask': input_encoding['attention_mask'].squeeze(),
            'labels': target_encoding['input_ids'].squeeze(),
            'task_type': sample['task_type']
        }


class DocumentTrainer:
    """文档学习训练器"""

    def __init__(
        self,
        model: DocumentLearnerModel,
        tokenizer: AutoTokenizer,
        train_dataset: DocumentDataset,
        val_dataset: Optional[DocumentDataset] = None,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
    ):
        self.model = model.to(device)
        self.tokenizer = tokenizer
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        self.device = device

        # 优化器和调度器
        self.optimizer = AdamW(
            self.model.parameters(),
            lr=config.training.learning_rate,
            weight_decay=config.training.weight_decay
        )

        # 计算总训练步数
        total_steps = len(train_dataset) // config.training.batch_size * config.training.num_epochs
        self.scheduler = get_linear_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=config.training.warmup_steps,
            num_training_steps=total_steps
        )

        # 训练状态
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')

        # 初始化wandb
        if wandb.run is None:
            wandb.init(
                project="document-learner",
                config=config.__dict__
            )

    def train(self):
        """开始训练"""
        print("开始训练...")

        train_loader = DataLoader(
            self.train_dataset,
            batch_size=config.training.batch_size,
            shuffle=True,
            num_workers=4
        )

        val_loader = None
        if self.val_dataset:
            val_loader = DataLoader(
                self.val_dataset,
                batch_size=config.training.batch_size,
                shuffle=False,
                num_workers=4
            )

        for epoch in range(config.training.num_epochs):
            self.epoch = epoch
            print(f"\nEpoch {epoch + 1}/{config.training.num_epochs}")

            # 训练一个epoch
            train_loss = self._train_epoch(train_loader)

            # 验证
            val_loss = None
            if val_loader:
                val_loss = self._validate(val_loader)

            # 记录日志
            self._log_epoch(train_loss, val_loss)

            # 保存检查点
            if self.global_step % config.training.save_steps == 0:
                self._save_checkpoint()

            # 早停检查
            if val_loss and val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self._save_best_model()

        print("训练完成！")

    def _train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0

        progress_bar = tqdm(train_loader, desc=f"Training Epoch {self.epoch + 1}")

        for batch in progress_bar:
            # 移动数据到设备
            batch = {k: v.to(self.device) for k, v in batch.items() if k != 'task_type'}

            # 前向传播
            outputs = self.model(**batch)
            loss = outputs['loss']

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), config.training.max_grad_norm)

            # 优化器步骤
            if (self.global_step + 1) % config.training.gradient_accumulation_steps == 0:
                self.optimizer.step()
                self.scheduler.step()
                self.optimizer.zero_grad()

            # 更新统计
            total_loss += loss.item()
            num_batches += 1
            self.global_step += 1

            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'avg_loss': f'{total_loss / num_batches:.4f}',
                'lr': f'{self.scheduler.get_last_lr()[0]:.2e}'
            })

            # 记录训练日志
            if self.global_step % config.training.logging_steps == 0:
                wandb.log({
                    'train/loss': loss.item(),
                    'train/learning_rate': self.scheduler.get_last_lr()[0],
                    'train/global_step': self.global_step
                })

        return total_loss / num_batches

    def _validate(self, val_loader: DataLoader) -> float:
        """验证模型"""
        self.model.eval()
        total_loss = 0
        num_batches = 0

        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                # 移动数据到设备
                batch = {k: v.to(self.device) for k, v in batch.items() if k != 'task_type'}

                # 前向传播
                outputs = self.model(**batch)
                loss = outputs['loss']

                total_loss += loss.item()
                num_batches += 1

        avg_loss = total_loss / num_batches
        return avg_loss

    def _log_epoch(self, train_loss: float, val_loss: Optional[float] = None):
        """记录epoch日志"""
        log_dict = {
            'epoch': self.epoch + 1,
            'train/epoch_loss': train_loss,
        }

        if val_loss is not None:
            log_dict['val/epoch_loss'] = val_loss

        wandb.log(log_dict)

        print(f"Epoch {self.epoch + 1} - Train Loss: {train_loss:.4f}", end="")
        if val_loss is not None:
            print(f", Val Loss: {val_loss:.4f}")
        else:
            print()

    def _save_checkpoint(self):
        """保存检查点"""
        checkpoint_dir = os.path.join(config.paths.output_dir, f"checkpoint-{self.global_step}")
        os.makedirs(checkpoint_dir, exist_ok=True)

        # 保存模型
        self.model.save_pretrained(checkpoint_dir)
        self.tokenizer.save_pretrained(checkpoint_dir)

        # 保存训练状态
        torch.save({
            'epoch': self.epoch,
            'global_step': self.global_step,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
        }, os.path.join(checkpoint_dir, 'training_state.pt'))

        print(f"检查点已保存到: {checkpoint_dir}")

    def _save_best_model(self):
        """保存最佳模型"""
        best_model_dir = os.path.join(config.paths.model_dir, "best_model")
        os.makedirs(best_model_dir, exist_ok=True)

        self.model.save_pretrained(best_model_dir)
        self.tokenizer.save_pretrained(best_model_dir)

        print(f"最佳模型已保存到: {best_model_dir}")

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        # 加载模型
        self.model = DocumentLearnerModel.from_pretrained(checkpoint_path)
        self.model.to(self.device)

        # 加载训练状态
        state_path = os.path.join(checkpoint_path, 'training_state.pt')
        if os.path.exists(state_path):
            state = torch.load(state_path, map_location=self.device)
            self.epoch = state['epoch']
            self.global_step = state['global_step']
            self.optimizer.load_state_dict(state['optimizer_state_dict'])
            self.scheduler.load_state_dict(state['scheduler_state_dict'])
            self.best_val_loss = state['best_val_loss']

            print(f"检查点已加载: epoch {self.epoch}, step {self.global_step}")


async def prepare_training_data(document_paths: List[str]) -> Tuple[List[Dict], List[Dict]]:
    """准备训练数据"""
    processor = DocumentProcessor()
    builder = DatasetBuilder(processor)

    print("正在处理文档...")
    all_samples = await builder.build_training_dataset(document_paths)

    # 分割数据集
    np.random.shuffle(all_samples)

    train_size = int(len(all_samples) * config.data.train_split)
    val_size = int(len(all_samples) * config.data.val_split)

    train_samples = all_samples[:train_size]
    val_samples = all_samples[train_size:train_size + val_size]

    print(f"训练样本数量: {len(train_samples)}")
    print(f"验证样本数量: {len(val_samples)}")

    return train_samples, val_samples


def main_training_pipeline(document_paths: List[str]):
    """主训练流程"""
    # 准备数据
    train_samples, val_samples = asyncio.run(prepare_training_data(document_paths))

    # 初始化tokenizer
    tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-medium")
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # 创建数据集
    train_dataset = DocumentDataset(train_samples, tokenizer, config.data.max_seq_length)
    val_dataset = DocumentDataset(val_samples, tokenizer, config.data.max_seq_length) if val_samples else None

    # 创建模型
    model = create_model(vocab_size=tokenizer.vocab_size)

    # 创建训练器
    trainer = DocumentTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=train_dataset,
        val_dataset=val_dataset
    )

    # 开始训练
    trainer.train()

    return trainer


if __name__ == "__main__":
    # 示例：训练JavaScript文档学习模型
    js_docs = [
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction",
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types",
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling"
    ]

    trainer = main_training_pipeline(js_docs)
    print("训练完成！")
