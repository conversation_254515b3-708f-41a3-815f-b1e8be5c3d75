# 文档学习小模型 (Document Learning Small Model)

一个能够通过阅读文档学习技能的小型语言模型。给它一个 JavaScript 文档，它就能学会使用 JavaScript！

## 🌟 特性

- **多语言学习**: 支持任何编程语言和技术框架的文档学习
- **文档学习**: 能够从技术文档中学习概念和语法
- **多任务训练**: 支持掩码语言建模和问答任务
- **小型架构**: 参数量控制在 100M 以下，适合个人设备训练
- **异步处理**: 高效的文档处理和数据准备
- **交互式推理**: 支持实时对话和文档学习
- **跨语言理解**: 能够比较和解释不同语言间的差异

## 🏗️ 架构

- **模型**: 基于 Transformer 的小型语言模型
- **训练**: 预训练 + 微调的两阶段训练
- **数据**: 支持 Markdown、纯文本和网页文档
- **推理**: 支持文本生成、问答、代码生成等多种任务

## 📦 安装

1. 克隆项目:

```bash
git clone <repository-url>
cd ai-model
```

2. 安装依赖:

```bash
pip install -r requirements.txt
```

3. 配置环境:

```bash
# 可选：配置wandb用于训练监控
wandb login
```

## 🚀 快速开始

### 1. 训练模型

使用 JavaScript 文档训练模型：

```bash
python train.py --documents \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction" \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types" \
    "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Control_flow_and_error_handling" \
    --num_epochs 5 \
    --batch_size 8
```

使用本地文档：

```bash
python train.py --documents \
    "./docs/javascript_basics.md" \
    "./docs/functions.md" \
    "./docs/objects.md"
```

### 2. 测试模型

基本功能测试：

```bash
python test_model.py ./models/best_model
```

交互式测试：

```bash
python test_model.py ./models/best_model --interactive
```

测试文档学习：

```bash
python test_model.py ./models/best_model \
    --test_document "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions"
```

### 3. 使用模型

```python
from src.inference import DocumentLearnerInference

# 加载模型
inferencer = DocumentLearnerInference("./models/best_model")

# 学习新文档
result = await inferencer.learn_from_document("path/to/document.md")

# 回答问题
answer = inferencer.answer_question("什么是JavaScript函数？")

# 生成代码
code = inferencer.generate_code("创建一个计算斐波那契数列的函数", "javascript")

# 解释概念
explanation = inferencer.explain_concept("闭包")
```

## ⚙️ 配置

主要配置在 `config.yaml` 中：

```yaml
# 模型配置
model:
  hidden_size: 512 # 隐藏层大小
  num_hidden_layers: 8 # 层数
  num_attention_heads: 8 # 注意力头数

# 训练配置
training:
  batch_size: 16 # 批次大小
  learning_rate: 5e-4 # 学习率
  num_epochs: 10 # 训练轮数

# 数据配置
data:
  max_seq_length: 1024 # 最大序列长度
  doc_chunk_size: 512 # 文档块大小
```

## 📊 训练监控

项目集成了 Wandb 用于训练监控：

- 训练损失和验证损失
- 学习率变化
- 模型性能指标

## 🔧 高级用法

### 自定义数据处理

```python
from src.data_processor import DocumentProcessor, DatasetBuilder

processor = DocumentProcessor()
builder = DatasetBuilder(processor)

# 处理自定义文档
chunks = await processor.process_markdown_file("custom_doc.md")
samples = await builder.build_training_dataset(["doc1.md", "doc2.md"])
```

### 自定义模型架构

```python
from src.model import DocumentLearnerConfig, DocumentLearnerModel

# 自定义配置
config = DocumentLearnerConfig(
    hidden_size=256,
    num_hidden_layers=6,
    num_attention_heads=4
)

# 创建模型
model = DocumentLearnerModel(config)
```

### 继续训练

```bash
python train.py --documents "new_docs.md" \
    --resume_from "./outputs/checkpoint-1000"
```

## 📁 项目结构

```
ai-model/
├── src/
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── data_processor.py  # 数据处理
│   ├── model.py          # 模型架构
│   ├── trainer.py        # 训练逻辑
│   └── inference.py      # 推理接口
├── train.py              # 训练脚本
├── test_model.py         # 测试脚本
├── config.yaml           # 配置文件
├── requirements.txt      # 依赖列表
└── README.md            # 说明文档
```

## 🌍 支持的语言和技术

### 编程语言

- **Python** - 数据科学、Web 开发、自动化
- **JavaScript/TypeScript** - 前端开发、Node.js
- **Java** - 企业级应用、Android 开发
- **C++** - 系统编程、游戏开发
- **Go** - 云原生、微服务
- **Rust** - 系统安全编程
- **Swift** - iOS 开发
- **Kotlin** - Android 开发、后端开发
- **PHP** - Web 后端开发
- **Ruby** - Web 框架、脚本

### 框架和库

- **前端**: React, Vue.js, Angular, Svelte
- **后端**: Django, Flask, Spring Boot, Express.js, FastAPI
- **移动端**: React Native, Flutter
- **机器学习**: TensorFlow, PyTorch, Scikit-learn
- **数据库**: SQL, MongoDB, Redis, PostgreSQL
- **云服务**: AWS, Azure, Google Cloud
- **DevOps**: Docker, Kubernetes, Jenkins

### 多语言训练示例

```bash
# 训练Python专用模型
make train-python

# 训练多语言通用模型
make train-multilang

# 训练特定语言组合
python examples/multi_language_training.py
```

## 🎯 使用场景

1. **多语言学习**: 同时掌握多种编程语言
2. **技术文档学习**: 快速学习新的编程语言或框架
3. **代码助手**: 基于文档生成代码示例
4. **概念解释**: 解释技术概念和术语
5. **语言比较**: 比较不同编程语言的特性和语法
6. **问答系统**: 基于文档内容回答技术问题

## 🔍 多语言示例对话

### JavaScript 学习示例

```
你: learn https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions
助手: 已学习文档 'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions'
文档摘要: 该文档介绍了JavaScript中函数的定义、调用和使用方法...
学到的概念: 函数声明, 函数表达式, 箭头函数, 参数, 返回值

你: 如何创建一个JavaScript函数？
助手: 在JavaScript中，有几种创建函数的方法：

1. 函数声明：
function myFunction() {
    // 函数体
}

2. 函数表达式：
const myFunction = function() {
    // 函数体
};

3. 箭头函数：
const myFunction = () => {
    // 函数体
};

你: 生成一个计算圆面积的函数
助手: function calculateCircleArea(radius) {
    return Math.PI * radius * radius;
}
```

### Python 学习示例

```
你: learn examples/docs/python_tutorial.md
助手: 已学习Python教程文档
学到的概念: 变量, 函数, 类, 列表, 字典

你: Python和JavaScript在函数定义上有什么区别？
助手: Python和JavaScript在函数定义上有以下主要区别：

1. 语法差异：
   - Python: def function_name():
   - JavaScript: function functionName() {}

2. 缩进 vs 大括号：
   - Python使用缩进表示代码块
   - JavaScript使用大括号{}

3. 类型注解：
   - Python支持类型提示: def add(a: int, b: int) -> int:
   - JavaScript原生不支持，需要TypeScript

你: 用Python写一个类
助手: class Person:
    def __init__(self, name, age):
        self.name = name
        self.age = age

    def introduce(self):
        return f"我是{self.name}，今年{self.age}岁"
```

### 跨语言比较示例

```
你: Go语言和Python在变量声明上有什么区别？
助手: Go语言和Python在变量声明上有显著区别：

1. 类型系统：
   - Go是静态类型：var name string = "张三"
   - Python是动态类型：name = "张三"

2. 声明方式：
   - Go有多种声明方式：var, :=
   - Python直接赋值即声明

3. 类型推断：
   - Go: name := "张三" (自动推断为string)
   - Python: name = "张三" (运行时确定类型)

你: 哪种语言更适合初学者？
助手: 对于初学者，我推荐Python，原因如下：

1. 语法简洁：Python语法接近自然语言
2. 动态类型：不需要预先声明变量类型
3. 丰富生态：大量学习资源和库
4. 应用广泛：Web开发、数据科学、AI等

Go语言虽然简洁，但静态类型对初学者可能有一定门槛。
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- Hugging Face Transformers
- PyTorch
- 所有开源贡献者
