#!/usr/bin/env python3
"""
使用示例脚本
"""
import asyncio
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.trainer import main_training_pipeline
from src.inference import DocumentLearnerInference


async def example_training():
    """训练示例"""
    print("🚀 开始训练示例...")
    
    # 使用一些简单的JavaScript文档进行训练
    js_docs = [
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction",
        "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types"
    ]
    
    print("📚 训练文档:")
    for doc in js_docs:
        print(f"  - {doc}")
    
    try:
        # 开始训练（这里只是演示，实际训练需要更多时间）
        trainer = main_training_pipeline(js_docs)
        print("✅ 训练完成！")
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False


async def example_inference(model_path: str):
    """推理示例"""
    print(f"🤖 开始推理示例...")
    print(f"模型路径: {model_path}")
    
    try:
        # 创建推理器
        inferencer = DocumentLearnerInference(model_path)
        
        print("\n📝 示例1: 基本文本生成")
        prompt = "JavaScript是一种"
        response = inferencer.generate_text(prompt, max_new_tokens=50)
        print(f"输入: {prompt}")
        print(f"输出: {response}")
        
        print("\n❓ 示例2: 问答")
        question = "什么是JavaScript变量？"
        answer = inferencer.answer_question(question)
        print(f"问题: {question}")
        print(f"答案: {answer}")
        
        print("\n💻 示例3: 代码生成")
        description = "创建一个函数来问候用户"
        code = inferencer.generate_code(description, "javascript")
        print(f"描述: {description}")
        print(f"生成的代码:\n{code}")
        
        print("\n📖 示例4: 概念解释")
        concept = "函数"
        explanation = inferencer.explain_concept(concept)
        print(f"概念: {concept}")
        print(f"解释: {explanation}")
        
        print("\n📚 示例5: 文档学习")
        test_doc = "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Functions"
        result = await inferencer.learn_from_document(test_doc)
        
        if "error" not in result:
            print(f"✅ 成功学习文档: {test_doc}")
            print(f"文档摘要: {result['summary'][:100]}...")
            print(f"学到的概念: {', '.join(result['learned_concepts'][:3])}")
            
            # 基于学习的文档回答问题
            context_question = "基于刚学习的文档，函数有哪些定义方式？"
            context_answer = inferencer.answer_question(context_question, result['summary'])
            print(f"\n基于文档的问答:")
            print(f"问题: {context_question}")
            print(f"答案: {context_answer}")
        else:
            print(f"❌ 学习文档失败: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_sample_documents():
    """创建示例文档"""
    print("📄 创建示例文档...")
    
    # 创建data目录
    os.makedirs("data", exist_ok=True)
    
    # JavaScript基础文档
    js_basics = """# JavaScript 基础

## 什么是JavaScript？

JavaScript是一种高级的、解释型的编程语言。它是Web开发的核心技术之一。

## 变量

在JavaScript中，可以使用var、let或const来声明变量：

```javascript
var name = "张三";
let age = 25;
const PI = 3.14159;
```

## 函数

函数是可重复使用的代码块：

```javascript
function greet(name) {
    return "你好，" + name + "！";
}

// 调用函数
console.log(greet("世界"));
```

## 对象

对象是键值对的集合：

```javascript
const person = {
    name: "李四",
    age: 30,
    city: "北京"
};
```
"""
    
    with open("data/javascript_basics.md", "w", encoding="utf-8") as f:
        f.write(js_basics)
    
    # Python基础文档
    python_basics = """# Python 基础

## 什么是Python？

Python是一种高级的、通用的编程语言，以其简洁和可读性著称。

## 变量

Python中的变量不需要声明类型：

```python
name = "张三"
age = 25
pi = 3.14159
```

## 函数

使用def关键字定义函数：

```python
def greet(name):
    return f"你好，{name}！"

# 调用函数
print(greet("世界"))
```

## 列表

列表是有序的可变集合：

```python
fruits = ["苹果", "香蕉", "橙子"]
fruits.append("葡萄")
```
"""
    
    with open("data/python_basics.md", "w", encoding="utf-8") as f:
        f.write(python_basics)
    
    print("✅ 示例文档已创建:")
    print("  - data/javascript_basics.md")
    print("  - data/python_basics.md")


async def run_complete_example():
    """运行完整示例"""
    print("🎯 文档学习模型完整示例")
    print("=" * 50)
    
    # 创建示例文档
    create_sample_documents()
    
    print("\n选择运行模式:")
    print("1. 仅训练模型")
    print("2. 仅测试推理（需要已训练的模型）")
    print("3. 完整流程（训练 + 推理）")
    print("4. 使用本地文档训练")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        success = await example_training()
        if success:
            print("\n🎉 训练示例完成！")
            print("模型保存在: ./models/best_model")
    
    elif choice == "2":
        model_path = input("请输入模型路径 (默认: ./models/best_model): ").strip()
        if not model_path:
            model_path = "./models/best_model"
        
        if os.path.exists(model_path):
            await example_inference(model_path)
        else:
            print(f"❌ 模型路径不存在: {model_path}")
    
    elif choice == "3":
        print("\n🚀 开始完整流程...")
        
        # 先训练
        success = await example_training()
        
        if success:
            print("\n⏳ 等待3秒后开始推理测试...")
            await asyncio.sleep(3)
            
            # 再推理
            model_path = "./models/best_model"
            if os.path.exists(model_path):
                await example_inference(model_path)
            else:
                print("❌ 找不到训练好的模型")
    
    elif choice == "4":
        print("\n📚 使用本地文档训练...")
        local_docs = ["data/javascript_basics.md", "data/python_basics.md"]
        
        try:
            trainer = main_training_pipeline(local_docs)
            print("✅ 本地文档训练完成！")
            
            # 测试推理
            model_path = "./models/best_model"
            if os.path.exists(model_path):
                print("\n🧪 测试训练结果...")
                await example_inference(model_path)
        except Exception as e:
            print(f"❌ 本地文档训练失败: {e}")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    try:
        asyncio.run(run_complete_example())
    except KeyboardInterrupt:
        print("\n⚠ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 运行示例时出错: {e}")
        import traceback
        traceback.print_exc()
