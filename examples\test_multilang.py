#!/usr/bin/env python3
"""
多语言模型测试脚本
"""
import sys
import os
import asyncio

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.inference import DocumentLearnerInference


# 不同语言的测试用例
TEST_CASES = {
    'python': {
        'questions': [
            "如何在Python中定义变量？",
            "Python中的函数如何定义？",
            "什么是Python中的列表？",
            "如何创建Python类？"
        ],
        'code_tasks': [
            "创建一个计算两个数之和的函数",
            "定义一个Person类，包含姓名和年龄属性",
            "创建一个列表并添加元素",
            "写一个循环打印1到10的数字"
        ],
        'concepts': [
            "变量", "函数", "类", "列表", "字典", "循环"
        ]
    },
    
    'javascript': {
        'questions': [
            "JavaScript中如何声明变量？",
            "什么是JavaScript函数？",
            "如何创建JavaScript对象？",
            "JavaScript中的数组如何使用？"
        ],
        'code_tasks': [
            "创建一个计算圆面积的函数",
            "定义一个用户对象包含姓名和邮箱",
            "创建一个数组并遍历元素",
            "写一个条件判断语句"
        ],
        'concepts': [
            "变量", "函数", "对象", "数组", "条件语句", "循环"
        ]
    },
    
    'go': {
        'questions': [
            "Go语言中如何声明变量？",
            "Go中的函数如何定义？",
            "什么是Go的结构体？",
            "Go中的接口是什么？"
        ],
        'code_tasks': [
            "创建一个返回两个值的函数",
            "定义一个Person结构体",
            "实现一个简单的接口",
            "写一个for循环"
        ],
        'concepts': [
            "变量", "函数", "结构体", "接口", "切片", "映射"
        ]
    },
    
    'react': {
        'questions': [
            "什么是React组件？",
            "React中的state是什么？",
            "如何处理React事件？",
            "什么是React的props？"
        ],
        'code_tasks': [
            "创建一个简单的函数组件",
            "实现一个带状态的计数器组件",
            "创建一个表单组件",
            "写一个列表渲染组件"
        ],
        'concepts': [
            "组件", "状态", "属性", "事件", "JSX", "钩子"
        ]
    }
}


async def test_language_understanding(inferencer: DocumentLearnerInference, language: str):
    """测试特定语言的理解能力"""
    if language not in TEST_CASES:
        print(f"❌ 不支持的语言测试: {language}")
        return
    
    test_case = TEST_CASES[language]
    print(f"\n🧪 测试 {language.upper()} 语言理解能力")
    print("=" * 50)
    
    # 测试问答能力
    print(f"\n📝 {language.upper()} 问答测试:")
    for i, question in enumerate(test_case['questions'], 1):
        print(f"\n{i}. {question}")
        try:
            answer = inferencer.answer_question(question)
            print(f"答案: {answer}")
        except Exception as e:
            print(f"❌ 回答失败: {e}")
    
    # 测试代码生成
    print(f"\n💻 {language.upper()} 代码生成测试:")
    for i, task in enumerate(test_case['code_tasks'], 1):
        print(f"\n{i}. {task}")
        try:
            code = inferencer.generate_code(task, language)
            print(f"生成的代码:\n```{language}\n{code}\n```")
        except Exception as e:
            print(f"❌ 代码生成失败: {e}")
    
    # 测试概念解释
    print(f"\n📖 {language.upper()} 概念解释测试:")
    for i, concept in enumerate(test_case['concepts'][:3], 1):  # 只测试前3个概念
        print(f"\n{i}. 解释'{concept}':")
        try:
            explanation = inferencer.explain_concept(f"{language}中的{concept}")
            print(f"解释: {explanation}")
        except Exception as e:
            print(f"❌ 概念解释失败: {e}")


async def test_cross_language_ability(inferencer: DocumentLearnerInference):
    """测试跨语言能力"""
    print("\n🌍 跨语言能力测试")
    print("=" * 50)
    
    cross_language_questions = [
        "Python和JavaScript在变量声明上有什么区别？",
        "Go语言和Python在函数定义上有什么不同？",
        "React组件和Python类有什么相似之处？",
        "比较不同语言中的循环语法",
        "哪种语言更适合Web开发？"
    ]
    
    for i, question in enumerate(cross_language_questions, 1):
        print(f"\n{i}. {question}")
        try:
            answer = inferencer.answer_question(question)
            print(f"答案: {answer}")
        except Exception as e:
            print(f"❌ 回答失败: {e}")


async def test_learning_new_language(inferencer: DocumentLearnerInference):
    """测试学习新语言的能力"""
    print("\n📚 测试学习新语言能力")
    print("=" * 50)
    
    # 模拟学习Rust文档
    rust_doc_content = """
# Rust 基础

Rust是一种系统编程语言，注重安全性和性能。

## 变量和可变性

```rust
fn main() {
    let x = 5; // 不可变变量
    let mut y = 5; // 可变变量
    y = 6;
    
    println!("x = {}, y = {}", x, y);
}
```

## 所有权

Rust的核心特性是所有权系统，它确保内存安全。

```rust
fn main() {
    let s1 = String::from("hello");
    let s2 = s1; // s1的所有权转移给s2
    // println!("{}", s1); // 这会报错，因为s1不再有效
    println!("{}", s2);
}
```
"""
    
    print("🔍 让模型学习Rust文档...")
    
    # 创建临时文档文件
    os.makedirs("examples/temp", exist_ok=True)
    temp_doc_path = "examples/temp/rust_basics.md"
    
    with open(temp_doc_path, "w", encoding="utf-8") as f:
        f.write(rust_doc_content)
    
    try:
        # 学习文档
        result = await inferencer.learn_from_document(temp_doc_path)
        
        if "error" not in result:
            print("✅ 成功学习Rust文档")
            print(f"学到的概念: {', '.join(result['learned_concepts'][:5])}")
            
            # 测试学习效果
            rust_questions = [
                "Rust中如何声明变量？",
                "什么是Rust的所有权？",
                "Rust中可变和不可变变量有什么区别？"
            ]
            
            print("\n📝 基于新学习的Rust知识回答问题:")
            for question in rust_questions:
                answer = inferencer.answer_question(question, result['summary'])
                print(f"Q: {question}")
                print(f"A: {answer}\n")
        else:
            print(f"❌ 学习失败: {result['error']}")
    
    except Exception as e:
        print(f"❌ 学习过程出错: {e}")
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_doc_path):
            os.remove(temp_doc_path)


def interactive_multilang_test(inferencer: DocumentLearnerInference):
    """交互式多语言测试"""
    print("\n🤖 交互式多语言测试")
    print("=" * 50)
    print("可用命令:")
    print("  test <language> - 测试特定语言 (python/javascript/go/react)")
    print("  code <language> <description> - 生成代码")
    print("  explain <concept> - 解释概念")
    print("  compare <lang1> <lang2> - 比较两种语言")
    print("  quit - 退出")
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            parts = user_input.split(' ', 2)
            command = parts[0].lower()
            
            if command == 'test' and len(parts) >= 2:
                language = parts[1].lower()
                asyncio.run(test_language_understanding(inferencer, language))
            
            elif command == 'code' and len(parts) >= 3:
                language = parts[1].lower()
                description = parts[2]
                code = inferencer.generate_code(description, language)
                print(f"生成的{language}代码:\n```{language}\n{code}\n```")
            
            elif command == 'explain' and len(parts) >= 2:
                concept = ' '.join(parts[1:])
                explanation = inferencer.explain_concept(concept)
                print(f"解释: {explanation}")
            
            elif command == 'compare' and len(parts) >= 3:
                lang1, lang2 = parts[1], parts[2]
                question = f"{lang1}和{lang2}有什么区别？"
                answer = inferencer.answer_question(question)
                print(f"比较结果: {answer}")
            
            else:
                print("❌ 无效命令或参数不足")
        
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 执行命令时出错: {e}")


async def main():
    """主函数"""
    print("🌍 多语言文档学习模型测试")
    print("=" * 50)
    
    # 选择模型
    print("可用模型:")
    print("1. 通用模型 (models/best_model)")
    print("2. 多语言模型 (models/multilang_model)")
    print("3. Python专用模型 (models/python_model)")
    print("4. 自定义路径")
    
    choice = input("\n请选择模型 (1-4): ").strip()
    
    model_paths = {
        '1': "models/best_model",
        '2': "models/multilang_model", 
        '3': "models/python_model"
    }
    
    if choice in model_paths:
        model_path = model_paths[choice]
    elif choice == '4':
        model_path = input("请输入模型路径: ").strip()
    else:
        print("❌ 无效选择")
        return
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return
    
    try:
        # 加载模型
        print(f"📦 加载模型: {model_path}")
        inferencer = DocumentLearnerInference(model_path)
        
        # 选择测试模式
        print("\n测试模式:")
        print("1. 全面测试所有语言")
        print("2. 测试特定语言")
        print("3. 跨语言能力测试")
        print("4. 学习新语言测试")
        print("5. 交互式测试")
        
        test_choice = input("\n请选择测试模式 (1-5): ").strip()
        
        if test_choice == '1':
            # 测试所有支持的语言
            for language in TEST_CASES.keys():
                await test_language_understanding(inferencer, language)
            await test_cross_language_ability(inferencer)
        
        elif test_choice == '2':
            language = input(f"请选择语言 ({'/'.join(TEST_CASES.keys())}): ").strip().lower()
            await test_language_understanding(inferencer, language)
        
        elif test_choice == '3':
            await test_cross_language_ability(inferencer)
        
        elif test_choice == '4':
            await test_learning_new_language(inferencer)
        
        elif test_choice == '5':
            interactive_multilang_test(inferencer)
        
        else:
            print("❌ 无效选择")
    
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
