#!/usr/bin/env python3
"""
多语言训练示例
"""
import sys
import os
import asyncio

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.trainer import main_training_pipeline


# 不同编程语言的文档资源
LANGUAGE_DOCS = {
    'python': [
        "https://docs.python.org/3/tutorial/introduction.html",
        "https://docs.python.org/3/tutorial/controlflow.html",
        "https://docs.python.org/3/tutorial/datastructures.html",
        "https://docs.python.org/3/tutorial/modules.html"
    ],
    
    'java': [
        "https://docs.oracle.com/javase/tutorial/java/nutsandbolts/variables.html",
        "https://docs.oracle.com/javase/tutorial/java/javaOO/methods.html",
        "https://docs.oracle.com/javase/tutorial/java/javaOO/classes.html",
        "https://docs.oracle.com/javase/tutorial/java/concepts/index.html"
    ],
    
    'cpp': [
        "https://en.cppreference.com/w/cpp/language/basic_concepts",
        "https://en.cppreference.com/w/cpp/language/functions",
        "https://en.cppreference.com/w/cpp/language/classes",
        "https://en.cppreference.com/w/cpp/container"
    ],
    
    'go': [
        "https://go.dev/tour/basics/1",
        "https://go.dev/tour/flowcontrol/1", 
        "https://go.dev/tour/moretypes/1",
        "https://go.dev/tour/methods/1"
    ],
    
    'rust': [
        "https://doc.rust-lang.org/book/ch03-01-variables-and-mutability.html",
        "https://doc.rust-lang.org/book/ch03-03-how-functions-work.html",
        "https://doc.rust-lang.org/book/ch04-01-what-is-ownership.html",
        "https://doc.rust-lang.org/book/ch05-01-defining-structs.html"
    ],
    
    'react': [
        "https://react.dev/learn/your-first-component",
        "https://react.dev/learn/importing-and-exporting-components",
        "https://react.dev/learn/writing-markup-with-jsx",
        "https://react.dev/learn/javascript-in-jsx-with-curly-braces"
    ],
    
    'django': [
        "https://docs.djangoproject.com/en/4.2/intro/tutorial01/",
        "https://docs.djangoproject.com/en/4.2/intro/tutorial02/",
        "https://docs.djangoproject.com/en/4.2/topics/db/models/",
        "https://docs.djangoproject.com/en/4.2/topics/http/views/"
    ],
    
    'tensorflow': [
        "https://www.tensorflow.org/guide/tensor",
        "https://www.tensorflow.org/guide/variable",
        "https://www.tensorflow.org/guide/autodiff",
        "https://www.tensorflow.org/guide/keras/sequential_model"
    ]
}


def create_local_docs():
    """创建本地示例文档"""
    os.makedirs("examples/docs", exist_ok=True)
    
    # Python示例文档
    python_doc = """# Python 基础教程

## 变量和数据类型

Python是动态类型语言，变量不需要声明类型：

```python
# 数字
age = 25
price = 99.99

# 字符串
name = "张三"
message = '你好，世界！'

# 布尔值
is_student = True
is_working = False

# 列表
fruits = ["苹果", "香蕉", "橙子"]
numbers = [1, 2, 3, 4, 5]

# 字典
person = {
    "name": "李四",
    "age": 30,
    "city": "北京"
}
```

## 函数

使用def关键字定义函数：

```python
def greet(name):
    return f"你好，{name}！"

def add_numbers(a, b):
    return a + b

def calculate_area(radius):
    import math
    return math.pi * radius ** 2
```

## 类和对象

```python
class Person:
    def __init__(self, name, age):
        self.name = name
        self.age = age
    
    def introduce(self):
        return f"我是{self.name}，今年{self.age}岁"

# 创建对象
person = Person("王五", 25)
print(person.introduce())
```
"""
    
    # Go示例文档
    go_doc = """# Go 语言基础

## 变量声明

Go是静态类型语言，有多种变量声明方式：

```go
package main

import "fmt"

func main() {
    // 方式1：var关键字
    var name string = "张三"
    var age int = 25
    
    // 方式2：类型推断
    var city = "北京"
    
    // 方式3：短变量声明
    email := "<EMAIL>"
    
    fmt.Println(name, age, city, email)
}
```

## 函数

```go
// 基本函数
func greet(name string) string {
    return "你好，" + name + "！"
}

// 多返回值
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, fmt.Errorf("除数不能为零")
    }
    return a / b, nil
}

// 方法（结构体函数）
type Person struct {
    Name string
    Age  int
}

func (p Person) Introduce() string {
    return fmt.Sprintf("我是%s，今年%d岁", p.Name, p.Age)
}
```

## 结构体和接口

```go
// 结构体
type Rectangle struct {
    Width  float64
    Height float64
}

// 接口
type Shape interface {
    Area() float64
}

// 实现接口
func (r Rectangle) Area() float64 {
    return r.Width * r.Height
}
```
"""
    
    # React示例文档
    react_doc = """# React 基础教程

## 组件

React应用由组件构成，组件是可重用的UI片段：

```jsx
// 函数组件
function Welcome(props) {
    return <h1>你好，{props.name}！</h1>;
}

// 箭头函数组件
const Greeting = ({ name }) => {
    return <div>欢迎，{name}！</div>;
};

// 使用组件
function App() {
    return (
        <div>
            <Welcome name="张三" />
            <Greeting name="李四" />
        </div>
    );
}
```

## State和Props

```jsx
import { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);
    
    const increment = () => {
        setCount(count + 1);
    };
    
    return (
        <div>
            <p>计数：{count}</p>
            <button onClick={increment}>增加</button>
        </div>
    );
}
```

## 事件处理

```jsx
function LoginForm() {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    
    const handleSubmit = (e) => {
        e.preventDefault();
        console.log('登录:', username, password);
    };
    
    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="用户名"
            />
            <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="密码"
            />
            <button type="submit">登录</button>
        </form>
    );
}
```
"""
    
    # 保存文档
    with open("examples/docs/python_tutorial.md", "w", encoding="utf-8") as f:
        f.write(python_doc)
    
    with open("examples/docs/go_tutorial.md", "w", encoding="utf-8") as f:
        f.write(go_doc)
    
    with open("examples/docs/react_tutorial.md", "w", encoding="utf-8") as f:
        f.write(react_doc)
    
    print("✅ 本地示例文档已创建")


async def train_language_specific_model(language: str):
    """训练特定语言的模型"""
    if language not in LANGUAGE_DOCS:
        print(f"❌ 不支持的语言: {language}")
        print(f"支持的语言: {', '.join(LANGUAGE_DOCS.keys())}")
        return
    
    print(f"🚀 开始训练 {language.upper()} 专用模型...")
    
    docs = LANGUAGE_DOCS[language]
    print(f"📚 使用文档:")
    for doc in docs:
        print(f"  - {doc}")
    
    try:
        # 设置输出目录
        import sys
        sys.path.append('..')
        from src.config import config
        config.paths.output_dir = f"./outputs/{language}_model"
        config.paths.model_dir = f"./models/{language}_model"
        
        # 开始训练
        trainer = main_training_pipeline(docs)
        print(f"✅ {language.upper()} 模型训练完成！")
        print(f"模型保存在: ./models/{language}_model/best_model")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")


async def train_multi_language_model():
    """训练多语言通用模型"""
    print("🌍 训练多语言通用模型...")
    
    # 合并多种语言的文档
    all_docs = []
    selected_languages = ['python', 'javascript', 'go', 'react']
    
    for lang in selected_languages:
        if lang == 'javascript':
            # JavaScript使用之前定义的文档
            js_docs = [
                "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Introduction",
                "https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Grammar_and_types"
            ]
            all_docs.extend(js_docs)
        else:
            all_docs.extend(LANGUAGE_DOCS[lang][:2])  # 每种语言取前2个文档
    
    print(f"📚 使用 {len(all_docs)} 个文档训练多语言模型")
    
    try:
        import sys
        sys.path.append('..')
        from src.config import config
        config.paths.output_dir = "./outputs/multilang_model"
        config.paths.model_dir = "./models/multilang_model"
        
        trainer = main_training_pipeline(all_docs)
        print("✅ 多语言模型训练完成！")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")


def main():
    """主函数"""
    print("🌍 多语言文档学习模型训练")
    print("=" * 50)
    
    # 创建本地文档
    create_local_docs()
    
    print("\n选择训练模式:")
    print("1. 训练特定语言模型")
    print("2. 训练多语言通用模型") 
    print("3. 使用本地文档训练")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        print(f"\n支持的语言: {', '.join(LANGUAGE_DOCS.keys())}")
        language = input("请输入要训练的语言: ").strip().lower()
        asyncio.run(train_language_specific_model(language))
        
    elif choice == "2":
        asyncio.run(train_multi_language_model())
        
    elif choice == "3":
        print("\n📚 使用本地文档训练...")
        local_docs = [
            "examples/docs/python_tutorial.md",
            "examples/docs/go_tutorial.md", 
            "examples/docs/react_tutorial.md"
        ]
        
        try:
            import sys
            sys.path.append('..')
            from src.config import config
            config.paths.output_dir = "./outputs/local_model"
            config.paths.model_dir = "./models/local_model"
            
            trainer = main_training_pipeline(local_docs)
            print("✅ 本地文档模型训练完成！")
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
    
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
