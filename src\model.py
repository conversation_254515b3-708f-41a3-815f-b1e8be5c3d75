"""
文档学习模型架构
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import (
    PreTrainedModel,
    PretrainedConfig,
    AutoTokenizer,
    AutoModel
)
from typing import Optional, Tuple, Dict, Any
import math
from .config import config


class DocumentLearnerConfig(PretrainedConfig):
    """文档学习模型配置"""

    model_type = "document_learner"

    def __init__(
        self,
        vocab_size: int = 32000,
        hidden_size: int = 512,
        num_hidden_layers: int = 8,
        num_attention_heads: int = 8,
        intermediate_size: int = 2048,
        max_position_embeddings: int = 2048,
        dropout: float = 0.1,
        layer_norm_eps: float = 1e-12,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.intermediate_size = intermediate_size
        self.max_position_embeddings = max_position_embeddings
        self.dropout = dropout
        self.layer_norm_eps = layer_norm_eps


class MultiHeadAttention(nn.Module):
    """多头注意力机制"""

    def __init__(self, config: DocumentLearnerConfig):
        super().__init__()
        self.num_attention_heads = config.num_attention_heads
        self.attention_head_size = config.hidden_size // config.num_attention_heads
        self.all_head_size = self.num_attention_heads * self.attention_head_size

        self.query = nn.Linear(config.hidden_size, self.all_head_size)
        self.key = nn.Linear(config.hidden_size, self.all_head_size)
        self.value = nn.Linear(config.hidden_size, self.all_head_size)

        self.dropout = nn.Dropout(config.dropout)
        self.dense = nn.Linear(config.hidden_size, config.hidden_size)
        self.layer_norm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)

    def transpose_for_scores(self, x: torch.Tensor) -> torch.Tensor:
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        # 计算注意力
        query_layer = self.transpose_for_scores(self.query(hidden_states))
        key_layer = self.transpose_for_scores(self.key(hidden_states))
        value_layer = self.transpose_for_scores(self.value(hidden_states))

        # 计算注意力分数
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))
        attention_scores = attention_scores / math.sqrt(self.attention_head_size)

        if attention_mask is not None:
            attention_scores = attention_scores + attention_mask

        attention_probs = F.softmax(attention_scores, dim=-1)
        attention_probs = self.dropout(attention_probs)

        # 应用注意力
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(*new_context_layer_shape)

        # 输出投影和残差连接
        attention_output = self.dense(context_layer)
        attention_output = self.dropout(attention_output)
        attention_output = self.layer_norm(attention_output + hidden_states)

        return attention_output


class FeedForward(nn.Module):
    """前馈网络"""

    def __init__(self, config: DocumentLearnerConfig):
        super().__init__()
        self.dense_1 = nn.Linear(config.hidden_size, config.intermediate_size)
        self.dense_2 = nn.Linear(config.intermediate_size, config.hidden_size)
        self.dropout = nn.Dropout(config.dropout)
        self.layer_norm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)

    def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
        # 前馈网络
        intermediate = F.gelu(self.dense_1(hidden_states))
        output = self.dense_2(intermediate)
        output = self.dropout(output)

        # 残差连接和层归一化
        output = self.layer_norm(output + hidden_states)
        return output


class TransformerLayer(nn.Module):
    """Transformer层"""

    def __init__(self, config: DocumentLearnerConfig):
        super().__init__()
        self.attention = MultiHeadAttention(config)
        self.feed_forward = FeedForward(config)

    def forward(
        self,
        hidden_states: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        attention_output = self.attention(hidden_states, attention_mask)
        layer_output = self.feed_forward(attention_output)
        return layer_output


class DocumentLearnerModel(PreTrainedModel):
    """文档学习模型"""

    config_class = DocumentLearnerConfig

    def __init__(self, config: DocumentLearnerConfig):
        super().__init__(config)
        self.config = config

        # 嵌入层
        self.embeddings = nn.Embedding(config.vocab_size, config.hidden_size)
        self.position_embeddings = nn.Embedding(
            config.max_position_embeddings,
            config.hidden_size
        )

        # Transformer层
        self.layers = nn.ModuleList([
            TransformerLayer(config) for _ in range(config.num_hidden_layers)
        ])

        # 输出层
        self.layer_norm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.dropout)

        # 语言建模头
        self.lm_head = nn.Linear(config.hidden_size, config.vocab_size, bias=False)

        # 初始化权重
        self.init_weights()

    def get_input_embeddings(self):
        return self.embeddings

    def set_input_embeddings(self, value):
        self.embeddings = value

    def forward(
        self,
        input_ids: Optional[torch.Tensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        position_ids: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        **kwargs
    ) -> Dict[str, torch.Tensor]:

        batch_size, seq_length = input_ids.size()

        # 位置编码
        if position_ids is None:
            position_ids = torch.arange(seq_length, dtype=torch.long, device=input_ids.device)
            position_ids = position_ids.unsqueeze(0).expand(batch_size, -1)

        # 嵌入
        inputs_embeds = self.embeddings(input_ids)
        position_embeds = self.position_embeddings(position_ids)
        hidden_states = inputs_embeds + position_embeds
        hidden_states = self.dropout(hidden_states)

        # 注意力掩码
        if attention_mask is not None:
            extended_attention_mask = attention_mask[:, None, None, :]
            extended_attention_mask = extended_attention_mask.to(dtype=hidden_states.dtype)
            extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0
        else:
            extended_attention_mask = None

        # 通过Transformer层
        for layer in self.layers:
            hidden_states = layer(hidden_states, extended_attention_mask)

        hidden_states = self.layer_norm(hidden_states)

        # 语言建模输出
        lm_logits = self.lm_head(hidden_states)

        outputs = {"logits": lm_logits, "hidden_states": hidden_states}

        # 计算损失
        if labels is not None:
            shift_logits = lm_logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()

            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, self.config.vocab_size), shift_labels.view(-1))
            outputs["loss"] = loss

        return outputs

    def generate(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        max_new_tokens: int = 50,
        temperature: float = 1.0,
        top_p: float = 0.9,
        do_sample: bool = True,
        pad_token_id: Optional[int] = None,
        eos_token_id: Optional[int] = None,
        **kwargs
    ) -> torch.Tensor:
        """生成文本序列"""
        self.eval()

        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        # 设置默认值
        if pad_token_id is None:
            pad_token_id = self.config.vocab_size - 1
        if eos_token_id is None:
            eos_token_id = pad_token_id

        # 初始化生成序列
        generated = input_ids.clone()

        # 扩展attention_mask
        if attention_mask is None:
            attention_mask = torch.ones_like(input_ids)

        with torch.no_grad():
            for _ in range(max_new_tokens):
                # 前向传播
                outputs = self.forward(
                    input_ids=generated,
                    attention_mask=attention_mask
                )

                # 获取下一个token的logits
                next_token_logits = outputs["logits"][:, -1, :]

                # 应用温度
                if temperature != 1.0:
                    next_token_logits = next_token_logits / temperature

                # 采样或贪心选择
                if do_sample:
                    # Top-p采样
                    if top_p < 1.0:
                        sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
                        cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                        # 移除累积概率超过top_p的token
                        sorted_indices_to_remove = cumulative_probs > top_p
                        sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                        sorted_indices_to_remove[..., 0] = 0

                        indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                        next_token_logits[indices_to_remove] = float('-inf')

                    # 多项式采样
                    probs = F.softmax(next_token_logits, dim=-1)
                    next_token = torch.multinomial(probs, num_samples=1)
                else:
                    # 贪心选择
                    next_token = torch.argmax(next_token_logits, dim=-1, keepdim=True)

                # 添加到生成序列
                generated = torch.cat([generated, next_token], dim=-1)

                # 更新attention_mask
                attention_mask = torch.cat([
                    attention_mask,
                    torch.ones((batch_size, 1), device=device)
                ], dim=-1)

                # 检查是否生成了结束token
                if (next_token == eos_token_id).all():
                    break

        return generated


def create_model(vocab_size: int = None) -> DocumentLearnerModel:
    """创建模型实例"""
    model_config = DocumentLearnerConfig(
        vocab_size=vocab_size or config.model.vocab_size,
        hidden_size=config.model.hidden_size,
        num_hidden_layers=config.model.num_hidden_layers,
        num_attention_heads=config.model.num_attention_heads,
        intermediate_size=config.model.intermediate_size,
        max_position_embeddings=config.model.max_position_embeddings,
        dropout=config.model.dropout,
        layer_norm_eps=config.model.layer_norm_eps
    )

    model = DocumentLearnerModel(model_config)

    # 打印模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"模型总参数数量: {total_params:,}")
    print(f"可训练参数数量: {trainable_params:,}")

    return model


if __name__ == "__main__":
    # 测试模型
    model = create_model()

    # 测试前向传播
    batch_size, seq_length = 2, 128
    input_ids = torch.randint(0, 1000, (batch_size, seq_length))
    attention_mask = torch.ones(batch_size, seq_length)

    with torch.no_grad():
        outputs = model(input_ids=input_ids, attention_mask=attention_mask)
        print(f"输出logits形状: {outputs['logits'].shape}")
        print(f"隐藏状态形状: {outputs['hidden_states'].shape}")
