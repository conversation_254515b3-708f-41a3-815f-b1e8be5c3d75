# 模型配置
model:
  name: "DocumentLearner"
  vocab_size: 32000
  hidden_size: 512
  num_hidden_layers: 8
  num_attention_heads: 8
  intermediate_size: 2048
  max_position_embeddings: 2048
  dropout: 0.1
  layer_norm_eps: 1e-12

# 训练配置
training:
  batch_size: 16
  learning_rate: 5e-4
  num_epochs: 10
  warmup_steps: 1000
  weight_decay: 0.01
  gradient_accumulation_steps: 4
  max_grad_norm: 1.0
  save_steps: 500
  eval_steps: 500
  logging_steps: 100

# 数据配置
data:
  max_seq_length: 1024
  doc_chunk_size: 512
  overlap_size: 64
  train_split: 0.8
  val_split: 0.1
  test_split: 0.1

# 路径配置
paths:
  data_dir: "./data"
  output_dir: "./outputs"
  model_dir: "./models"
  logs_dir: "./logs"
  cache_dir: "./cache"

# 推理配置
inference:
  max_new_tokens: 256
  temperature: 0.7
  top_p: 0.9
  do_sample: true
